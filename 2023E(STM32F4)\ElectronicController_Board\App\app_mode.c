// Copyright (c) 2024 米醋电子工作室
// 文件名: app_mode.c
// 描述: 系统模式管理实现文件，处理不同工作模式的切换和管理

#include "app_mode.h"
#include "app_pid.h"
#include "app_uasrt.h"
#include "app_maixcam.h"
#include "app_motor.h"

// 当前系统模式
static system_mode_t current_mode = MODE_IDLE;

// 模式二相关变量
static uint8_t mode2_no_data_flag = 0;  // 无数据标志位：1=无新数据，0=有新数据
static uint32_t mode2_last_data_time = 0;  // 上次接收数据的时间戳
static uint8_t mode2_x_speed = 90;  // X轴转动速度百分比
static uint8_t mode2_data_verify_count = 0;  // 数据验证计数器 (0-3)
static int mode2_last_verified_x = -1;       // 上次验证的X坐标
static int mode2_last_verified_y = -1;       // 上次验证的Y坐标



// 模式名称数组
static const char* mode_names[] = {
    "IDLE",      // 空闲模式
    "TRACKING",  // 追踪模式
    "CUSTOM"     // 智能搜索模式
};

/**
 * @brief 模式管理初始化
 */
void app_mode_init(void)
{
    current_mode = MODE_IDLE; // 上电默认为空闲模式

    // 确保MaixCam回调函数为默认回调（支持模式检查）
    maixcam_set_callback(NULL); // NULL会恢复为默认回调函数

    // 确保PID处于停止状态
    app_pid_stop();



    my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
}

/**
 * @brief 模式切换函数
 */
void app_mode_switch(void)
{
    // 退出当前模式
    switch(current_mode)
    {
        case MODE_IDLE:
            // 空闲模式无需特殊退出处理
            break;
            
        case MODE_TRACKING:
            // 退出追踪模式：停止PID控制
            app_pid_stop();
            my_printf(&huart1, "Exit Tracking Mode\r\n");
            break;
            
        case MODE_CUSTOM:
            // 模式二退出处理：停止电机和定时器，重置验证状态
            Motor_Stop();
            Motor_Disable();  // 禁用电机
            mode2_no_data_flag = 0;
            mode2_data_verify_count = 0;  // 重置验证计数器
            mode2_last_verified_x = -1;
            mode2_last_verified_y = -1;
            my_printf(&huart1, "Exit Custom Mode\r\n");
            break;
            
        default:
            break;
    }
    
    // 切换到下一个模式（按照：空闲→追踪→空闲→自定义→空闲的循环）
    static uint8_t switch_count = 0; // 按键计数器

    switch_count++;

    if(switch_count == 1)
    {
        current_mode = MODE_TRACKING; // 第1次按键：进入追踪模式
    }
    else if(switch_count == 2)
    {
        current_mode = MODE_IDLE;     // 第2次按键：回到空闲模式
    }
    else if(switch_count == 3)
    {
        current_mode = MODE_CUSTOM;   // 第3次按键：进入自定义模式
    }
    else
    {
        current_mode = MODE_IDLE;     // 第4次按键：回到空闲模式
        switch_count = 0;             // 重置计数器，下次从追踪模式开始
    }
    
    // 进入新模式
    switch(current_mode)
    {
        case MODE_IDLE:
	
            break;
            
        case MODE_TRACKING:
            // 进入追踪模式：启动PID控制
            my_printf(&huart1, "Enter Tracking Mode\r\n");
            app_pid_init();
            app_pid_start();
            break;
            
        case MODE_CUSTOM:
            // 模式二进入处理：初始化相关变量和定时器
            mode2_no_data_flag = 1;  // 初始设为无数据状态
            mode2_last_data_time = HAL_GetTick();
            mode2_data_verify_count = 0;  // 重置验证计数器
            mode2_last_verified_x = -1;   // 重置验证坐标
            mode2_last_verified_y = -1;

            // 使能电机并启动X轴旋转搜索
            Motor_Enable();
            Motor_Set_Speed(mode2_x_speed, 0);

            // 启动模式二监控定时器
            multiTimerStart(&mt_user, 100, mode2_monitor_task, NULL);  // 100ms检查一次
            my_printf(&huart1, "Enter Custom Mode - X-axis rotation searching\r\n");
            break;
            
        default:
            break;
    }
    
    my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
}

/**
 * @brief 获取当前模式
 * @return 当前系统模式
 */
system_mode_t app_mode_get_current(void)
{
    return current_mode;
}

/**
 * @brief 获取模式名称
 * @param mode 模式枚举值
 * @return 模式名称字符串
 */
const char* app_mode_get_name(system_mode_t mode)
{
    if(mode < MODE_MAX)
        return mode_names[mode];
    else
        return "UNKNOWN";
}

/**
 * @brief 验证数据稳定性（连续3次检测）
 * @return true=数据稳定可切换，false=数据未稳定
 */
bool mode2_verify_stable_data(void)
{
    extern int target_x_coord, target_y_coord;  // 来自app_maixcam.c的全局变量

    // 检查坐标是否发生变化
    if (target_x_coord != mode2_last_verified_x || target_y_coord != mode2_last_verified_y) {

        // 数据合理性检查（假设有效范围0-640, 0-480）
        if (target_x_coord >= 0 && target_x_coord <= 640 &&
            target_y_coord >= 0 && target_y_coord <= 480) {

            // 更新验证坐标
            mode2_last_verified_x = target_x_coord;
            mode2_last_verified_y = target_y_coord;

            // 验证计数器+1
            mode2_data_verify_count++;

            // 更新时间戳和标志位
            mode2_last_data_time = HAL_GetTick();
            mode2_no_data_flag = 0;

            my_printf(&huart1, "Mode2: Data verification (%d/3) - Target: X=%d, Y=%d\r\n",
                     mode2_data_verify_count, target_x_coord, target_y_coord);

            // 连续3次验证成功
            if (mode2_data_verify_count >= 3) {
                return true;  // 数据稳定，可以切换
            }
        } else {
            // 数据无效，重置计数器
            mode2_data_verify_count = 0;
            my_printf(&huart1, "Mode2: Invalid data, reset verification\r\n");
        }
    } else {
        // 无新数据，重置计数器
        if (mode2_data_verify_count > 0) {
            mode2_data_verify_count = 0;
            my_printf(&huart1, "Mode2: No new data, reset verification\r\n");
        }
    }

    return false;  // 数据未稳定
}

/**
 * @brief 模式二监控任务
 * @param timer 定时器指针
 * @param userData 用户数据
 */
void mode2_monitor_task(MultiTimer *timer, void *userData)
{
    if (current_mode != MODE_CUSTOM) {
        return;  // 不在模式二时退出
    }

    // 验证数据稳定性（X轴持续旋转）
    if (mode2_verify_stable_data()) {
        // 连续3次验证成功，切换到模式一
        my_printf(&huart1, "Mode2: Stable data verified (3/3) - Switch to Tracking Mode\r\n");
        current_mode = MODE_TRACKING;

        // 停止X轴旋转
        Motor_Stop();

        // 启动追踪模式
        app_pid_init();
        app_pid_start();

        my_printf(&huart1, "System Mode: %s\r\n", app_mode_get_name(current_mode));
        return;  // 切换模式后退出，不再重启定时器
    }

    // 重新启动定时器，继续监控（X轴持续旋转）
    multiTimerStart(&mt_user, 100, mode2_monitor_task, NULL);
}
